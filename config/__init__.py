# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  


from .base_config import *
from .db_config import *

# 根据平台导入对应配置
import os
platform = os.getenv('PLATFORM', globals().get('PLATFORM', 'xhs'))

if platform == "douyin" or platform == "dy":
    from .dy_config import *
elif platform == "xhs":
    from .xhs_config import *
elif platform == "bilibili" or platform == "bili":
    from .bilibili_config import *
elif platform == "kuaishou" or platform == "ks":
    from .ks_config import *
elif platform == "weibo" or platform == "wb":
    from .weibo_config import *
elif platform == "tieba":
    from .tieba_config import *
elif platform == "zhihu":
    from .zhihu_config import *