# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。

# 抖音平台配置
PUBLISH_TIME_TYPE = 0

# 指定DY视频ID列表
DY_SPECIFIED_ID_LIST = [
    "7280854932641664319",
    "7202432992642387233",
    # ........................
]

# 指定DY用户ID列表
DY_CREATOR_ID_LIST = [
    "MS4wLjABAAAATJPY7LAlaa5X-c8uNdWkvz0jUGgpw4eeXIwu_8BhvqE",
    # ........................
]

# 创作者中心配置
# 是否启用创作者中心功能
ENABLE_CREATOR_CENTER = False

# 创作者中心登录配置
CREATOR_LOGIN_TYPE = "qrcode"  # qrcode, phone, cookie
CREATOR_LOGIN_PHONE = ""  # 创作者登录手机号
CREATOR_COOKIES = ""  # 创作者中心cookies

# 创作者数据获取配置
CREATOR_DATA_DAYS = 7  # 获取多少天的数据分析，支持 1, 7, 30
ENABLE_GET_CREATOR_VIDEOS = True  # 是否获取创作者视频列表
ENABLE_GET_CREATOR_ANALYTICS = True  # 是否获取创作者数据分析
ENABLE_GET_FANS_ANALYTICS = True  # 是否获取粉丝数据分析
ENABLE_GET_COMMENTS_MANAGEMENT = True  # 是否获取评论管理数据
ENABLE_GET_INCOME_DATA = True  # 是否获取收益数据

# 创作者视频分析配置
ENABLE_VIDEO_ANALYTICS = True  # 是否获取每个视频的详细分析数据
MAX_CREATOR_VIDEOS_COUNT = 100  # 最大获取视频数量
