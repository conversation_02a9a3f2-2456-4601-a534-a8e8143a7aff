#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者中心接口测试

测试抖音创作者登录和数据获取功能
"""

import asyncio
import sys
import os
import unittest
from unittest.mock import Mock, AsyncMock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from media_platform.douyin.creator_client import DOUYINCreatorClient
from media_platform.douyin.login import DouYinCreatorLogin


class TestDouYinCreatorClient(unittest.TestCase):
    """抖音创作者客户端测试"""
    
    def setUp(self):
        """测试初始化"""
        self.mock_page = Mock()
        self.mock_page.evaluate = AsyncMock(return_value={})
        
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Cookie": "test_cookie=test_value",
            "Host": "creator.douyin.com",
            "Origin": "https://creator.douyin.com/",
            "Referer": "https://creator.douyin.com/",
            "Content-Type": "application/json;charset=UTF-8",
        }
        
        self.client = DOUYINCreatorClient(
            headers=self.headers,
            playwright_page=self.mock_page,
            cookie_dict={"test_cookie": "test_value"}
        )

    def test_client_initialization(self):
        """测试客户端初始化"""
        self.assertEqual(self.client._host, "https://creator.douyin.com")
        self.assertEqual(self.client.headers, self.headers)
        self.assertEqual(self.client.timeout, 30)

    @patch('requests.request')
    async def test_get_creator_info(self, mock_request):
        """测试获取创作者信息"""
        # 模拟响应数据
        mock_response = Mock()
        mock_response.json.return_value = {
            "status_code": 0,
            "data": {
                "user_info": {
                    "nickname": "测试创作者",
                    "follower_count": 10000,
                    "following_count": 100
                }
            }
        }
        mock_response.text = '{"status_code": 0}'
        mock_request.return_value = mock_response
        
        # 执行测试
        result = await self.client.get_creator_info()
        
        # 验证结果
        self.assertEqual(result["status_code"], 0)
        self.assertIn("data", result)

    @patch('requests.request')
    async def test_get_creator_videos(self, mock_request):
        """测试获取创作者视频列表"""
        # 模拟响应数据
        mock_response = Mock()
        mock_response.json.return_value = {
            "status_code": 0,
            "data": {
                "item_list": [
                    {
                        "item_id": "123456789",
                        "desc": "测试视频",
                        "statistics": {
                            "play_count": 1000,
                            "digg_count": 100
                        }
                    }
                ],
                "has_more": False,
                "cursor": 0
            }
        }
        mock_response.text = '{"status_code": 0}'
        mock_request.return_value = mock_response
        
        # 执行测试
        result = await self.client.get_creator_videos()
        
        # 验证结果
        self.assertEqual(result["status_code"], 0)
        self.assertIn("data", result)

    @patch('requests.request')
    async def test_get_creator_analytics(self, mock_request):
        """测试获取创作者数据分析"""
        # 模拟响应数据
        mock_response = Mock()
        mock_response.json.return_value = {
            "status_code": 0,
            "data": {
                "overview": {
                    "total_play": 50000,
                    "total_like": 5000,
                    "total_comment": 500,
                    "total_share": 100
                }
            }
        }
        mock_response.text = '{"status_code": 0}'
        mock_request.return_value = mock_response
        
        # 执行测试
        result = await self.client.get_creator_analytics(days=7)
        
        # 验证结果
        self.assertEqual(result["status_code"], 0)
        self.assertIn("data", result)


class TestDouYinCreatorLogin(unittest.TestCase):
    """抖音创作者登录测试"""
    
    def setUp(self):
        """测试初始化"""
        self.mock_browser_context = Mock()
        self.mock_page = Mock()
        self.mock_page.url = "https://creator.douyin.com"
        self.mock_page.goto = AsyncMock()
        self.mock_page.wait_for_selector = AsyncMock()
        self.mock_page.evaluate = AsyncMock(return_value={"HasUserLogin": "1"})
        
        self.login = DouYinCreatorLogin(
            login_type="qrcode",
            browser_context=self.mock_browser_context,
            context_page=self.mock_page,
            login_phone="",
            cookie_str=""
        )

    def test_login_initialization(self):
        """测试登录对象初始化"""
        self.assertEqual(self.login.creator_center_url, "https://creator.douyin.com")
        self.assertEqual(self.login.scan_qrcode_time, 60)

    async def test_check_creator_login_state_success(self):
        """测试检查登录状态成功"""
        # 模拟已登录状态
        self.mock_page.evaluate.return_value = {"HasUserLogin": "1"}
        
        # 执行测试
        result = await self.login.check_creator_login_state()
        
        # 验证结果
        self.assertTrue(result)

    async def test_check_creator_login_state_failed(self):
        """测试检查登录状态失败"""
        # 模拟未登录状态
        self.mock_page.evaluate.return_value = {"HasUserLogin": "0"}
        self.mock_page.query_selector_all = AsyncMock(return_value=[Mock()])  # 有登录按钮
        
        # 执行测试
        result = await self.login.check_creator_login_state()
        
        # 验证结果
        self.assertFalse(result)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_import_modules(self):
        """测试模块导入"""
        try:
            from media_platform.douyin.creator_client import DOUYINCreatorClient
            from media_platform.douyin.login import DouYinCreatorLogin
            from media_platform.douyin.core import DouYinCrawler
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"模块导入失败: {e}")

    def test_config_import(self):
        """测试配置导入"""
        try:
            import config
            # 检查是否有创作者相关配置
            self.assertTrue(hasattr(config, 'CRAWLER_TYPE'))
            self.assertTrue(hasattr(config, 'LOGIN_TYPE'))
        except ImportError as e:
            self.fail(f"配置导入失败: {e}")


async def run_async_tests():
    """运行异步测试"""
    print("=== 运行异步测试 ===")
    
    # 创建测试实例
    client_test = TestDouYinCreatorClient()
    client_test.setUp()
    
    login_test = TestDouYinCreatorLogin()
    login_test.setUp()
    
    try:
        # 测试客户端方法
        print("测试获取创作者信息...")
        await client_test.test_get_creator_info()
        print("✅ 获取创作者信息测试通过")
        
        print("测试获取创作者视频...")
        await client_test.test_get_creator_videos()
        print("✅ 获取创作者视频测试通过")
        
        print("测试获取数据分析...")
        await client_test.test_get_creator_analytics()
        print("✅ 获取数据分析测试通过")
        
        # 测试登录方法
        print("测试登录状态检查...")
        result = await login_test.test_check_creator_login_state_success()
        print("✅ 登录状态检查测试通过")
        
        print("🎉 所有异步测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    """主测试函数"""
    print("=== 抖音创作者中心接口测试 ===")
    
    # 运行同步测试
    print("\n1. 运行同步测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行异步测试
    print("\n2. 运行异步测试...")
    asyncio.run(run_async_tests())
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
