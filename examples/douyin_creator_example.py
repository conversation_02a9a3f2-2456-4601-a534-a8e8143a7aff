#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者中心接口使用示例

本示例展示如何使用抖音创作者接口进行登录和数据获取
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from media_platform.douyin.core import DouYinCrawler


async def main():
    """主函数"""
    print("=== 抖音创作者中心接口示例 ===")
    
    # 配置创作者中心参数
    config.PLATFORM = "douyin"
    config.CRAWLER_TYPE = "creator_center"  # 设置为创作者中心模式
    config.LOGIN_TYPE = "qrcode"  # 登录方式：qrcode, phone, cookie
    config.HEADLESS = False  # 显示浏览器窗口，方便扫码登录
    
    # 如果使用手机号登录，请设置手机号
    # config.LOGIN_TYPE = "phone"
    # config.LOGIN_PHONE = "你的手机号"
    
    # 如果使用cookie登录，请设置cookies
    # config.LOGIN_TYPE = "cookie"
    # config.COOKIES = "你的cookies字符串"
    
    # 创作者数据获取配置
    config.ENABLE_GET_CREATOR_VIDEOS = True
    config.ENABLE_GET_CREATOR_ANALYTICS = True
    config.ENABLE_GET_FANS_ANALYTICS = True
    config.ENABLE_GET_COMMENTS_MANAGEMENT = True
    config.ENABLE_GET_INCOME_DATA = True
    config.CREATOR_DATA_DAYS = 7  # 获取7天的数据
    
    # 创建爬虫实例
    crawler = DouYinCrawler()
    
    try:
        # 开始爬取
        await crawler.start()
        print("✅ 创作者中心数据获取完成")
        
    except KeyboardInterrupt:
        print("❌ 用户中断操作")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    finally:
        # 关闭浏览器
        await crawler.close()


async def creator_data_analysis_example():
    """创作者数据分析示例"""
    print("\n=== 创作者数据分析示例 ===")
    
    # 这里可以添加数据分析逻辑
    # 例如：分析视频表现、粉丝增长趋势、收益变化等
    
    print("📊 数据分析功能开发中...")
    print("可以在这里添加：")
    print("- 视频播放量分析")
    print("- 粉丝增长趋势分析") 
    print("- 评论互动分析")
    print("- 收益变化分析")
    print("- 内容表现对比")


def print_usage():
    """打印使用说明"""
    print("""
🚀 抖音创作者中心接口使用说明

1. 登录方式配置：
   - 二维码登录（推荐）：config.LOGIN_TYPE = "qrcode"
   - 手机号登录：config.LOGIN_TYPE = "phone" + 设置手机号
   - Cookie登录：config.LOGIN_TYPE = "cookie" + 设置cookies

2. 功能配置：
   - ENABLE_GET_CREATOR_VIDEOS: 获取创作者视频列表
   - ENABLE_GET_CREATOR_ANALYTICS: 获取创作者数据分析
   - ENABLE_GET_FANS_ANALYTICS: 获取粉丝数据分析
   - ENABLE_GET_COMMENTS_MANAGEMENT: 获取评论管理数据
   - ENABLE_GET_INCOME_DATA: 获取收益数据

3. 数据获取范围：
   - CREATOR_DATA_DAYS: 数据分析天数（1, 7, 30）
   - MAX_CREATOR_VIDEOS_COUNT: 最大获取视频数量

4. 注意事项：
   - 首次使用建议使用二维码登录
   - 请遵守平台使用条款，合理控制请求频率
   - 仅用于学习和研究目的

5. 运行示例：
   python examples/douyin_creator_example.py
    """)


if __name__ == "__main__":
    print_usage()
    
    # 运行主程序
    asyncio.run(main())
    
    # 运行数据分析示例
    asyncio.run(creator_data_analysis_example())
