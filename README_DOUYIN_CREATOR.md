# 抖音创作者中心接口

## 🎯 功能概述

本项目新增了抖音创作者中心接口，支持创作者登录和数据获取功能。通过该接口，创作者可以：

- 🔐 **安全登录**：支持二维码、手机号、Cookie三种登录方式
- 📊 **数据分析**：获取播放量、点赞数、评论数等详细数据
- 🎬 **视频管理**：获取所有视频列表和单个视频分析
- 👥 **粉丝洞察**：获取粉丝增长和画像数据
- 💬 **评论管理**：获取评论列表，支持回复功能
- 💰 **收益统计**：获取创作者收益数据

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基本配置

```python
import config

# 设置平台为抖音
config.PLATFORM = "douyin"

# 设置爬取类型为创作者中心
config.CRAWLER_TYPE = "creator_center"

# 设置登录方式（推荐二维码登录）
config.LOGIN_TYPE = "qrcode"
config.HEADLESS = False  # 显示浏览器窗口用于扫码
```

### 3. 运行示例

```python
from media_platform.douyin.core import DouYinCrawler
import asyncio

async def main():
    crawler = DouYinCrawler()
    try:
        await crawler.start()
        print("✅ 数据获取完成")
    finally:
        await crawler.close()

# 运行
asyncio.run(main())
```

### 4. 使用示例脚本

```bash
python examples/douyin_creator_example.py
```

## 📋 登录方式

### 二维码登录（推荐）
```python
config.LOGIN_TYPE = "qrcode"
config.HEADLESS = False  # 必须显示浏览器窗口
```

### 手机号登录
```python
config.LOGIN_TYPE = "phone"
config.LOGIN_PHONE = "你的手机号"
```

### Cookie登录
```python
config.LOGIN_TYPE = "cookie"
config.COOKIES = "你的cookies字符串"
```

## 🔧 配置选项

### 数据获取开关
```python
# 在 config/dy_config.py 中配置
ENABLE_GET_CREATOR_VIDEOS = True      # 获取视频列表
ENABLE_GET_CREATOR_ANALYTICS = True   # 获取数据分析
ENABLE_GET_FANS_ANALYTICS = True      # 获取粉丝分析
ENABLE_GET_COMMENTS_MANAGEMENT = True # 获取评论管理
ENABLE_GET_INCOME_DATA = True         # 获取收益数据
```

### 数据范围配置
```python
CREATOR_DATA_DAYS = 7                 # 数据分析天数（1, 7, 30）
MAX_CREATOR_VIDEOS_COUNT = 100        # 最大获取视频数量
```

## 📊 API接口

### DOUYINCreatorClient 主要方法

```python
# 获取创作者信息
creator_info = await client.get_creator_info()

# 获取视频列表
videos = await client.get_creator_videos(cursor=0, count=20)

# 获取视频分析数据
analytics = await client.get_video_analytics(item_id="视频ID")

# 获取创作者数据分析
overview = await client.get_creator_analytics(days=7)

# 获取粉丝分析
fans_data = await client.get_fans_analytics(days=7)

# 获取评论管理
comments = await client.get_comments_management()

# 获取收益概览
income = await client.get_income_overview(days=7)

# 获取所有视频（自动翻页）
all_videos = await client.get_all_creator_videos()
```

## 📁 文件结构

```
media_platform/douyin/
├── core.py              # 主爬虫类（已更新）
├── client.py            # 普通用户客户端
├── creator_client.py    # 创作者中心客户端（新增）
├── login.py             # 登录类（已更新，新增创作者登录）
├── exception.py         # 异常定义
├── field.py             # 字段定义
└── help.py              # 辅助函数

config/
├── dy_config.py         # 抖音配置（已更新）
└── base_config.py       # 基础配置（已更新）

examples/
└── douyin_creator_example.py  # 使用示例（新增）

docs/
└── douyin_creator_api.md       # 详细文档（新增）

tests/
└── test_douyin_creator.py      # 测试文件（新增）
```

## ⚠️ 注意事项

### 使用限制
- 仅供学习和研究使用
- 请遵守抖音平台使用条款
- 合理控制请求频率，避免被限制
- 妥善保管登录凭证和数据

### 技术要求
- Python 3.7+
- Playwright浏览器自动化
- 稳定的网络连接
- 有效的抖音创作者账号

### 常见问题

#### Q: 登录失败怎么办？
A: 
1. 检查网络连接
2. 确认账号状态正常
3. 尝试不同登录方式
4. 查看控制台错误信息

#### Q: 数据获取失败？
A:
1. 检查登录状态
2. 确认接口权限
3. 查看错误日志
4. 检查配置是否正确

#### Q: 如何处理验证码？
A:
1. 推荐使用二维码登录避免验证码
2. 手动处理滑动验证码
3. 降低请求频率

## 🧪 测试

运行测试脚本：
```bash
python tests/test_douyin_creator.py
```

## 📖 详细文档

查看完整API文档：[docs/douyin_creator_api.md](docs/douyin_creator_api.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目遵循原项目许可证，仅供学习和研究使用。

---

**免责声明**：本代码仅供学习和研究目的使用，请遵守相关法律法规和平台使用条款。
