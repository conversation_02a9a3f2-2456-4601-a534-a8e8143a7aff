# 抖音创作者中心HTTP API

## 🎯 项目概述

本项目为抖音创作者中心提供了完整的HTTP API接口，允许外部应用通过RESTful API访问抖音创作者功能。

### ✨ 核心功能

- 🔐 **创作者登录**：支持二维码、手机号、Cookie三种登录方式
- 📊 **数据分析**：获取播放量、点赞数、评论数等详细统计
- 🎬 **视频管理**：获取视频列表和单个视频详细分析
- 👥 **粉丝洞察**：获取粉丝增长和画像数据
- 💬 **评论管理**：获取评论列表，支持回复功能
- 💰 **收益统计**：获取创作者收益数据

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装基础依赖
pip install fastapi uvicorn pydantic aiohttp requests

# 或安装完整依赖
pip install -r api/requirements.txt
```

### 2. 启动API服务器

```bash
# 基本启动
python api/start_server.py

# 指定端口和主机
python api/start_server.py --host 0.0.0.0 --port 8080

# 开发模式（自动重载）
python api/start_server.py --reload --log-level debug
```

### 3. 访问API文档

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **API根路径**: http://localhost:8000/

### 4. 测试API

```bash
# 获取API信息
curl -X GET "http://localhost:8000/"

# 登录（二维码）
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"login_type": "qrcode", "headless": false}'

# 检查登录状态
curl -X GET "http://localhost:8000/auth/status"
```

## 📁 项目结构

```
api/
├── douyin_creator_api.py    # 主API服务器
├── client_example.py        # 客户端使用示例
├── start_server.py          # 服务器启动脚本
├── config.py                # API配置文件
└── requirements.txt         # 依赖列表

docs/
└── api_documentation.md     # 完整API文档

media_platform/douyin/
├── core.py                  # 爬虫核心（已更新）
├── creator_client.py        # 创作者客户端（新增）
└── login.py                 # 登录模块（已更新）
```

## 🔧 API接口概览

### 认证接口
- `POST /auth/login` - 创作者登录
- `GET /auth/status` - 检查登录状态
- `POST /auth/logout` - 登出

### 数据接口
- `GET /creator/info` - 获取创作者信息
- `POST /creator/videos` - 获取视频列表
- `GET /creator/videos/all` - 获取所有视频
- `POST /video/analytics` - 获取视频分析
- `POST /creator/analytics` - 获取创作者数据分析
- `POST /fans/analytics` - 获取粉丝分析

### 评论管理
- `GET /comments/management` - 获取评论管理
- `POST /comments/reply` - 回复评论

### 收益数据
- `POST /income/overview` - 获取收益概览

## 💻 使用示例

### Python客户端

```python
import asyncio
from api.client_example import DouYinCreatorAPIClient

async def main():
    async with DouYinCreatorAPIClient() as client:
        # 登录
        await client.login(login_type="qrcode")
        
        # 获取创作者信息
        info = await client.get_creator_info()
        print(f"创作者: {info['data']['user_info']['nickname']}")
        
        # 获取视频列表
        videos = await client.get_creator_videos(count=10)
        print(f"视频数量: {len(videos['data']['item_list'])}")

asyncio.run(main())
```

### JavaScript客户端

```javascript
// 登录
const loginResponse = await fetch('http://localhost:8000/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    login_type: 'qrcode',
    headless: false
  })
});

// 获取创作者信息
const infoResponse = await fetch('http://localhost:8000/creator/info');
const creatorInfo = await infoResponse.json();
console.log('创作者信息:', creatorInfo.data);
```

### cURL命令

```bash
# 登录
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"login_type": "qrcode", "headless": false}'

# 获取创作者信息
curl -X GET "http://localhost:8000/creator/info"

# 获取视频列表
curl -X POST "http://localhost:8000/creator/videos" \
  -H "Content-Type: application/json" \
  -d '{"cursor": 0, "count": 20}'
```

## 🔧 配置选项

### 环境变量

```bash
export API_HOST=0.0.0.0          # API服务器主机
export API_PORT=8000             # API服务器端口
export API_LOG_LEVEL=info        # 日志级别
export ENVIRONMENT=production    # 运行环境
```

### 配置文件

编辑 `api/config.py` 来自定义配置：

```python
# 服务器配置
API_HOST = "0.0.0.0"
API_PORT = 8000

# 数据配置
DATA_CONFIG = {
    "max_videos_per_request": 50,
    "default_analytics_days": 7,
    "request_interval": 1.0
}

# 安全配置
RATE_LIMIT_CONFIG = {
    "requests_per_minute": 60,
    "requests_per_hour": 1000
}
```

## 🛠️ 开发工具

### 启动开发服务器

```bash
python api/start_server.py --reload --log-level debug
```

### 检查依赖

```bash
python api/start_server.py --check-deps
```

### 运行客户端示例

```bash
python api/client_example.py
```

### 查看配置

```bash
python api/config.py
```

## 📊 监控和日志

### 健康检查

```bash
curl -X GET "http://localhost:8000/health"
```

### 日志配置

日志文件位置：`logs/api.log`

日志级别：DEBUG, INFO, WARNING, ERROR

### 性能监控

API提供基本的性能指标：

- 请求计数
- 响应时间
- 错误率
- 活跃连接数

## ⚠️ 注意事项

### 安全建议

1. **生产环境**：设置 `ENVIRONMENT=production`
2. **API密钥**：启用API密钥认证
3. **HTTPS**：使用反向代理配置HTTPS
4. **防火墙**：限制API访问来源

### 性能优化

1. **缓存**：启用响应缓存
2. **限流**：配置请求频率限制
3. **连接池**：优化数据库连接
4. **负载均衡**：多实例部署

### 错误处理

1. **重试机制**：实现指数退避重试
2. **超时设置**：合理设置请求超时
3. **错误日志**：记录详细错误信息
4. **监控告警**：设置错误率告警

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目遵循原项目许可证，仅供学习和研究使用。

## 🆘 支持

- **文档**: [docs/api_documentation.md](docs/api_documentation.md)
- **示例**: [api/client_example.py](api/client_example.py)
- **配置**: [api/config.py](api/config.py)

---

**免责声明**：本API仅供学习和研究目的使用，请遵守相关法律法规和平台使用条款。
