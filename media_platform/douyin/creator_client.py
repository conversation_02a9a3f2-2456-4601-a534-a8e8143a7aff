# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  


import asyncio
import copy
import json
import urllib.parse
from typing import Any, Callable, Dict, List, Optional

import requests
from playwright.async_api import BrowserContext, Page

from base.base_crawler import AbstractApiClient
from tools import utils

from .exception import DataFetchError
from .help import get_a_bogus, get_web_id


class DOUYINCreatorClient(AbstractApiClient):
    """抖音创作者中心客户端"""
    
    def __init__(
            self,
            timeout=30,
            proxies=None,
            *,
            headers: Dict,
            playwright_page: Optional[Page],
            cookie_dict: Dict
    ):
        self.proxies = proxies
        self.timeout = timeout
        self.headers = headers
        self._host = "https://creator.douyin.com"
        self.playwright_page = playwright_page
        self.cookie_dict = cookie_dict

    async def __process_req_params(
            self, uri: str, params: Optional[Dict] = None, headers: Optional[Dict] = None,
            request_method="GET"
    ):
        """处理请求参数，添加创作者中心特有的参数"""
        if not params:
            return
        headers = headers or self.headers
        
        try:
            local_storage: Dict = await self.playwright_page.evaluate("() => window.localStorage")
        except Exception:
            local_storage = {}
        
        # 创作者中心的通用参数
        common_params = {
            "device_platform": "webapp",
            "aid": "2906",  # 创作者中心的aid
            "channel": "channel_pc_web",
            "version_code": "190600",
            "version_name": "19.6.0",
            "update_version_code": "170400",
            "pc_client_type": "1",
            "cookie_enabled": "true",
            "browser_language": "zh-CN",
            "browser_platform": "MacIntel",
            "browser_name": "Chrome",
            "browser_version": "*********",
            "browser_online": "true",
            "engine_name": "Blink",
            "os_name": "Mac OS",
            "os_version": "10.15.7",
            "cpu_core_num": "8",
            "device_memory": "8",
            "engine_version": "109.0",
            "platform": "PC",
            "screen_width": "2560",
            "screen_height": "1440",
            'effective_type': '4g',
            "round_trip_time": "50",
            "webid": get_web_id(),
            "msToken": local_storage.get("xmst", ""),
        }
        params.update(common_params)
        query_string = urllib.parse.urlencode(params)

        # 生成a_bogus参数
        post_data = {}
        if request_method == "POST":
            post_data = params
        a_bogus = await get_a_bogus(uri, query_string, post_data, headers["User-Agent"], self.playwright_page)
        params["a_bogus"] = a_bogus

    async def request(self, method, url, **kwargs):
        """发送HTTP请求"""
        response = None
        try:
            if method == "GET":
                response = requests.request(method, url, timeout=self.timeout, proxies=self.proxies, **kwargs)
            elif method == "POST":
                response = requests.request(method, url, timeout=self.timeout, proxies=self.proxies, **kwargs)
            
            if response.text == "" or response.text == "blocked":
                utils.logger.error(f"Request params incorrect, response.text: {response.text}")
                raise Exception("Account blocked")
            
            return response.json()
        except Exception as e:
            raise DataFetchError(f"{e}, {response.text if response else 'No response'}")

    async def get(self, uri: str, params: Optional[Dict] = None, headers: Optional[Dict] = None):
        """GET请求"""
        await self.__process_req_params(uri, params, headers)
        headers = headers or self.headers
        return await self.request(method="GET", url=f"{self._host}{uri}", params=params, headers=headers)

    async def post(self, uri: str, data: dict, headers: Optional[Dict] = None):
        """POST请求"""
        await self.__process_req_params(uri, data, headers, request_method="POST")
        headers = headers or self.headers
        return await self.request(method="POST", url=f"{self._host}{uri}", json=data, headers=headers)

    async def pong(self, browser_context: BrowserContext) -> bool:
        """检查创作者中心登录状态"""
        try:
            # 检查当前页面URL
            current_url = self.playwright_page.url
            if "creator.douyin.com" not in current_url:
                return False
            
            # 检查localStorage
            local_storage = await self.playwright_page.evaluate("() => window.localStorage")
            if local_storage.get("HasUserLogin", "") == "1":
                return True

            # 检查cookies
            _, cookie_dict = utils.convert_cookies(await browser_context.cookies())
            return cookie_dict.get("LOGIN_STATUS") == "1"
        except Exception as e:
            utils.logger.warning(f"[DOUYINCreatorClient.pong] Check login state error: {e}")
            return False

    async def update_cookies(self, browser_context: BrowserContext):
        """更新cookies"""
        cookie_str, cookie_dict = utils.convert_cookies(await browser_context.cookies())
        self.headers["Cookie"] = cookie_str
        self.cookie_dict = cookie_dict

    async def get_creator_info(self) -> Dict:
        """获取创作者信息"""
        uri = "/aweme/v1/creator/data/item/"
        params = {
            "count": 20,
            "cursor": 0
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/home"
        return await self.get(uri, params, headers)

    async def get_creator_videos(self, cursor: int = 0, count: int = 20) -> Dict:
        """获取创作者视频列表"""
        uri = "/aweme/v1/creator/data/item/"
        params = {
            "cursor": cursor,
            "count": count,
            "search_key": "",
            "sort_type": 1,  # 1: 最新发布, 2: 最多播放
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/content"
        return await self.get(uri, params, headers)

    async def get_video_analytics(self, item_id: str) -> Dict:
        """获取视频数据分析"""
        uri = "/aweme/v1/creator/data/item/analysis/"
        params = {
            "item_id": item_id
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/content/detail"
        return await self.get(uri, params, headers)

    async def get_creator_analytics(self, days: int = 7) -> Dict:
        """获取创作者数据分析
        
        Args:
            days: 统计天数，支持 1, 7, 30
        """
        uri = "/aweme/v1/creator/data/overview/"
        params = {
            "days": days
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/data"
        return await self.get(uri, params, headers)

    async def get_fans_analytics(self, days: int = 7) -> Dict:
        """获取粉丝数据分析"""
        uri = "/aweme/v1/creator/data/fans/"
        params = {
            "days": days
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/data/fans"
        return await self.get(uri, params, headers)

    async def get_comments_management(self, cursor: int = 0, count: int = 20) -> Dict:
        """获取评论管理列表"""
        uri = "/aweme/v1/creator/comment/list/"
        params = {
            "cursor": cursor,
            "count": count,
            "filter_type": 0  # 0: 全部, 1: 待回复, 2: 已回复
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/content/comment"
        return await self.get(uri, params, headers)

    async def reply_comment(self, comment_id: str, reply_text: str, item_id: str) -> Dict:
        """回复评论"""
        uri = "/aweme/v1/creator/comment/reply/"
        data = {
            "comment_id": comment_id,
            "text": reply_text,
            "item_id": item_id
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/content/comment"
        return await self.post(uri, data, headers)

    async def get_income_overview(self, days: int = 7) -> Dict:
        """获取收益概览"""
        uri = "/aweme/v1/creator/income/overview/"
        params = {
            "days": days
        }
        headers = copy.copy(self.headers)
        headers["Referer"] = f"{self._host}/creator-micro/income"
        return await self.get(uri, params, headers)

    async def get_all_creator_videos(self, callback: Optional[Callable] = None) -> List[Dict]:
        """获取创作者所有视频"""
        result = []
        cursor = 0
        has_more = True
        
        while has_more:
            try:
                videos_res = await self.get_creator_videos(cursor=cursor, count=20)
                videos = videos_res.get("item_list", [])
                
                if not videos:
                    break
                
                result.extend(videos)
                
                if callback:
                    await callback(videos)
                
                # 检查是否还有更多数据
                has_more = videos_res.get("has_more", False)
                cursor = videos_res.get("cursor", 0)
                
                # 添加延迟避免请求过快
                await asyncio.sleep(1)
                
            except Exception as e:
                utils.logger.error(f"[DOUYINCreatorClient.get_all_creator_videos] Error: {e}")
                break
        
        return result
