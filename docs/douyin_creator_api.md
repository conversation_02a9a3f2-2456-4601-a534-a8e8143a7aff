# 抖音创作者中心接口文档

## 概述

本文档介绍如何使用抖音创作者中心接口进行登录和数据获取。创作者中心接口提供了丰富的创作者数据分析功能，包括视频数据、粉丝分析、收益统计等。

## 功能特性

### 🔐 登录功能
- **二维码登录**：支持扫码登录（推荐）
- **手机号登录**：支持手机验证码登录
- **Cookie登录**：支持使用已有cookies登录

### 📊 数据获取
- **创作者信息**：获取基本创作者信息
- **视频列表**：获取创作者所有视频
- **数据分析**：获取播放量、互动数据等分析
- **粉丝分析**：获取粉丝增长和画像数据
- **评论管理**：获取评论列表和管理功能
- **收益数据**：获取创作者收益统计

## 快速开始

### 1. 基本配置

```python
import config
from media_platform.douyin.core import DouYinCrawler

# 基本配置
config.PLATFORM = "douyin"
config.CRAWLER_TYPE = "creator_center"
config.LOGIN_TYPE = "qrcode"  # 登录方式
config.HEADLESS = False  # 显示浏览器窗口
```

### 2. 登录方式配置

#### 二维码登录（推荐）
```python
config.LOGIN_TYPE = "qrcode"
config.HEADLESS = False  # 需要显示浏览器扫码
```

#### 手机号登录
```python
config.LOGIN_TYPE = "phone"
config.LOGIN_PHONE = "你的手机号"
```

#### Cookie登录
```python
config.LOGIN_TYPE = "cookie"
config.COOKIES = "你的cookies字符串"
```

### 3. 功能开关配置

```python
# 数据获取配置
config.ENABLE_GET_CREATOR_VIDEOS = True      # 获取视频列表
config.ENABLE_GET_CREATOR_ANALYTICS = True   # 获取数据分析
config.ENABLE_GET_FANS_ANALYTICS = True      # 获取粉丝分析
config.ENABLE_GET_COMMENTS_MANAGEMENT = True # 获取评论管理
config.ENABLE_GET_INCOME_DATA = True         # 获取收益数据

# 数据范围配置
config.CREATOR_DATA_DAYS = 7                 # 数据分析天数
config.MAX_CREATOR_VIDEOS_COUNT = 100        # 最大视频数量
```

### 4. 运行示例

```python
async def main():
    crawler = DouYinCrawler()
    try:
        await crawler.start()
        print("数据获取完成")
    finally:
        await crawler.close()

# 运行
asyncio.run(main())
```

## API接口说明

### DOUYINCreatorClient 类

#### 基本方法

```python
# 检查登录状态
await client.pong(browser_context)

# 更新cookies
await client.update_cookies(browser_context)
```

#### 数据获取方法

```python
# 获取创作者信息
creator_info = await client.get_creator_info()

# 获取视频列表
videos = await client.get_creator_videos(cursor=0, count=20)

# 获取视频分析数据
analytics = await client.get_video_analytics(item_id="视频ID")

# 获取创作者数据分析
overview = await client.get_creator_analytics(days=7)

# 获取粉丝分析
fans_data = await client.get_fans_analytics(days=7)

# 获取评论管理
comments = await client.get_comments_management(cursor=0, count=20)

# 获取收益概览
income = await client.get_income_overview(days=7)
```

#### 批量操作

```python
# 获取所有视频（自动翻页）
all_videos = await client.get_all_creator_videos(callback=process_videos)
```

### DouYinCreatorLogin 类

#### 登录方法

```python
# 创建登录对象
login = DouYinCreatorLogin(
    login_type="qrcode",
    browser_context=browser_context,
    context_page=page,
    login_phone="手机号",
    cookie_str="cookies"
)

# 开始登录
await login.begin()
```

## 数据结构说明

### 创作者信息
```json
{
    "user_info": {
        "nickname": "创作者昵称",
        "avatar": "头像URL",
        "follower_count": 粉丝数,
        "following_count": 关注数
    }
}
```

### 视频数据
```json
{
    "item_id": "视频ID",
    "desc": "视频描述",
    "create_time": 创建时间,
    "statistics": {
        "play_count": 播放量,
        "digg_count": 点赞数,
        "comment_count": 评论数,
        "share_count": 分享数
    }
}
```

### 数据分析
```json
{
    "overview": {
        "total_play": 总播放量,
        "total_like": 总点赞数,
        "total_comment": 总评论数,
        "total_share": 总分享数
    },
    "trend": [
        {
            "date": "日期",
            "play_count": 播放量,
            "like_count": 点赞数
        }
    ]
}
```

## 注意事项

### ⚠️ 重要提醒
1. **合规使用**：请遵守抖音平台使用条款
2. **频率控制**：合理控制请求频率，避免被限制
3. **数据安全**：妥善保管登录凭证和数据
4. **仅供学习**：本代码仅供学习和研究使用

### 🔧 技术要求
- Python 3.7+
- Playwright浏览器自动化
- 稳定的网络连接
- 有效的抖音创作者账号

### 🐛 常见问题

#### 登录失败
- 检查网络连接
- 确认账号状态正常
- 尝试不同登录方式

#### 数据获取失败
- 检查登录状态
- 确认接口权限
- 查看错误日志

#### 验证码问题
- 手动处理滑动验证码
- 使用二维码登录避免验证码

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 新增创作者中心登录功能
- ✨ 新增创作者数据获取接口
- ✨ 新增视频分析功能
- ✨ 新增粉丝数据分析
- ✨ 新增评论管理功能
- ✨ 新增收益数据获取

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和平台使用条款。
