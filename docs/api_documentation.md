# 抖音创作者中心HTTP API文档

## 📋 概述

抖音创作者中心HTTP API提供了完整的RESTful接口，允许外部应用通过HTTP请求访问抖音创作者功能。

### 🌟 主要特性

- 🔐 **多种登录方式**：二维码、手机号、Cookie
- 📊 **数据分析**：播放量、点赞数、评论数等详细统计
- 🎬 **视频管理**：获取视频列表和详细分析
- 👥 **粉丝洞察**：粉丝增长和画像数据
- 💬 **评论管理**：评论列表和回复功能
- 💰 **收益统计**：创作者收益数据

### 🚀 快速开始

1. **启动API服务器**
```bash
python api/start_server.py
```

2. **访问API文档**
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

3. **测试API**
```bash
curl -X GET "http://localhost:8000/"
```

## 🔧 服务器配置

### 启动参数

```bash
python api/start_server.py [选项]

选项:
  --host HOST          服务器主机地址 (默认: 0.0.0.0)
  --port PORT          服务器端口 (默认: 8000)
  --reload             开启自动重载模式（开发用）
  --log-level LEVEL    日志级别 (debug|info|warning|error)
  --workers N          工作进程数量 (默认: 1)
  --check-deps         检查依赖并退出
```

### 环境变量

```bash
export API_HOST=0.0.0.0
export API_PORT=8000
export API_LOG_LEVEL=info
export ENVIRONMENT=production
```

## 📡 API接口

### 基础信息

- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`
- **响应格式**: JSON

### 通用响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔐 认证接口

### 1. 登录

**POST** `/auth/login`

登录到抖音创作者中心。

#### 请求参数

```json
{
  "login_type": "qrcode",  // 登录类型: qrcode, phone, cookie
  "phone": "13800138000",  // 手机号（phone登录时必填）
  "cookies": "cookie_string",  // Cookies（cookie登录时必填）
  "headless": false  // 是否无头模式
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "登录请求已提交，请等待登录完成",
  "data": {
    "login_type": "qrcode"
  }
}
```

#### cURL示例

```bash
# 二维码登录
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"login_type": "qrcode", "headless": false}'

# 手机号登录
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"login_type": "phone", "phone": "13800138000"}'
```

### 2. 检查登录状态

**GET** `/auth/status`

检查当前登录状态。

#### 响应示例

```json
{
  "code": 0,
  "message": "登录状态检查成功",
  "data": {
    "logged_in": true
  }
}
```

### 3. 登出

**POST** `/auth/logout`

退出登录。

#### 响应示例

```json
{
  "code": 0,
  "message": "登出成功",
  "data": null
}
```

## 📊 数据接口

### 1. 获取创作者信息

**GET** `/creator/info`

获取当前登录创作者的基本信息。

#### 响应示例

```json
{
  "code": 0,
  "message": "获取创作者信息成功",
  "data": {
    "user_info": {
      "nickname": "创作者昵称",
      "avatar": "头像URL",
      "follower_count": 10000,
      "following_count": 100
    }
  }
}
```

### 2. 获取视频列表

**POST** `/creator/videos`

获取创作者的视频列表。

#### 请求参数

```json
{
  "cursor": 0,  // 游标位置
  "count": 20   // 获取数量
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取视频列表成功",
  "data": {
    "item_list": [
      {
        "item_id": "123456789",
        "desc": "视频描述",
        "create_time": 1640995200,
        "statistics": {
          "play_count": 1000,
          "digg_count": 100,
          "comment_count": 50,
          "share_count": 10
        }
      }
    ],
    "has_more": true,
    "cursor": 20
  }
}
```

### 3. 获取所有视频

**GET** `/creator/videos/all`

获取创作者的所有视频（自动翻页）。

#### 响应示例

```json
{
  "code": 0,
  "message": "获取所有视频成功",
  "data": {
    "videos": [...],
    "total": 150
  }
}
```

### 4. 获取视频分析

**POST** `/video/analytics`

获取指定视频的详细分析数据。

#### 请求参数

```json
{
  "item_id": "123456789"
}
```

### 5. 获取创作者数据分析

**POST** `/creator/analytics`

获取创作者的数据分析。

#### 请求参数

```json
{
  "days": 7  // 分析天数: 1, 7, 30
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取创作者数据分析成功",
  "data": {
    "overview": {
      "total_play": 50000,
      "total_like": 5000,
      "total_comment": 500,
      "total_share": 100
    },
    "trend": [
      {
        "date": "2024-01-01",
        "play_count": 1000,
        "like_count": 100
      }
    ]
  }
}
```

### 6. 获取粉丝分析

**POST** `/fans/analytics`

获取粉丝数据分析。

#### 请求参数

```json
{
  "days": 7
}
```

## 💬 评论管理

### 1. 获取评论管理

**GET** `/comments/management`

获取评论管理数据。

#### 查询参数

- `cursor`: 游标位置 (默认: 0)
- `count`: 获取数量 (默认: 20)

#### 示例

```bash
curl -X GET "http://localhost:8000/comments/management?cursor=0&count=20"
```

### 2. 回复评论

**POST** `/comments/reply`

回复指定评论。

#### 请求参数

```json
{
  "comment_id": "comment123",
  "reply_text": "感谢支持！",
  "item_id": "video123"
}
```

## 💰 收益数据

### 获取收益概览

**POST** `/income/overview`

获取创作者收益统计。

#### 请求参数

```json
{
  "days": 7
}
```

## 🚨 错误处理

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录失效 |
| 429 | 请求频率过高 |
| 500 | 服务器内部错误 |

### 错误响应示例

```json
{
  "code": 401,
  "message": "请先登录创作者中心",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 📝 使用示例

### Python客户端

```python
import requests

# 登录
response = requests.post("http://localhost:8000/auth/login", json={
    "login_type": "qrcode",
    "headless": False
})

# 获取创作者信息
response = requests.get("http://localhost:8000/creator/info")
creator_info = response.json()
```

### JavaScript客户端

```javascript
// 登录
const loginResponse = await fetch('http://localhost:8000/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    login_type: 'qrcode',
    headless: false
  })
});

// 获取创作者信息
const infoResponse = await fetch('http://localhost:8000/creator/info');
const creatorInfo = await infoResponse.json();
```

## ⚠️ 注意事项

1. **登录要求**：大部分接口需要先登录
2. **请求频率**：请合理控制请求频率，避免被限制
3. **数据安全**：妥善保管登录凭证
4. **合规使用**：遵守平台使用条款
5. **错误处理**：请正确处理API错误响应

## 🔧 开发工具

- **API文档**: http://localhost:8000/docs
- **客户端示例**: `python api/client_example.py`
- **配置检查**: `python api/config.py`
- **依赖检查**: `python api/start_server.py --check-deps`
