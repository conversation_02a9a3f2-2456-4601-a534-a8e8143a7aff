#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者中心API配置文件

定义API服务的配置参数
"""

import os
from typing import Dict, Any

# ==================== 服务器配置 ====================

# API服务器配置
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8000"))
API_RELOAD = os.getenv("API_RELOAD", "false").lower() == "true"
API_LOG_LEVEL = os.getenv("API_LOG_LEVEL", "info")

# 跨域配置
CORS_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:8080",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:8080",
]

# ==================== 抖音配置 ====================

# 默认登录配置
DEFAULT_LOGIN_CONFIG = {
    "login_type": "qrcode",
    "headless": False,
    "timeout": 300  # 登录超时时间（秒）
}

# 数据获取配置
DATA_CONFIG = {
    "max_videos_per_request": 50,  # 单次请求最大视频数量
    "max_comments_per_request": 100,  # 单次请求最大评论数量
    "default_analytics_days": 7,  # 默认数据分析天数
    "request_interval": 1.0,  # 请求间隔（秒）
}

# ==================== 缓存配置 ====================

# 缓存配置
CACHE_CONFIG = {
    "enable": True,
    "ttl": 300,  # 缓存过期时间（秒）
    "max_size": 1000,  # 最大缓存条目数
}

# ==================== 安全配置 ====================

# API密钥配置（可选）
API_KEY = os.getenv("API_KEY", "")
ENABLE_API_KEY_AUTH = os.getenv("ENABLE_API_KEY_AUTH", "false").lower() == "true"

# 请求限制配置
RATE_LIMIT_CONFIG = {
    "enable": True,
    "requests_per_minute": 60,  # 每分钟最大请求数
    "requests_per_hour": 1000,  # 每小时最大请求数
}

# ==================== 日志配置 ====================

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "logs/api.log",
    "max_size": "10MB",
    "backup_count": 5
}

# ==================== 数据库配置 ====================

# 数据库配置（可选，用于存储API调用记录等）
DATABASE_CONFIG = {
    "enable": False,
    "url": os.getenv("DATABASE_URL", "sqlite:///api_data.db"),
    "echo": False
}

# ==================== 监控配置 ====================

# 监控配置
MONITORING_CONFIG = {
    "enable": True,
    "metrics_endpoint": "/metrics",
    "health_endpoint": "/health"
}

# ==================== 响应配置 ====================

# API响应配置
RESPONSE_CONFIG = {
    "include_timestamp": True,
    "include_request_id": True,
    "max_response_size": 10 * 1024 * 1024,  # 10MB
}

# ==================== 错误处理配置 ====================

# 错误处理配置
ERROR_CONFIG = {
    "include_traceback": False,  # 生产环境建议设为False
    "log_errors": True,
    "error_codes": {
        "LOGIN_REQUIRED": 401,
        "INVALID_PARAMS": 400,
        "RATE_LIMITED": 429,
        "INTERNAL_ERROR": 500
    }
}

# ==================== 功能开关 ====================

# 功能开关
FEATURE_FLAGS = {
    "enable_video_upload": False,  # 视频上传功能（未实现）
    "enable_live_streaming": False,  # 直播功能（未实现）
    "enable_batch_operations": True,  # 批量操作
    "enable_webhook": False,  # Webhook功能（未实现）
}

# ==================== 环境配置 ====================

# 环境配置
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
DEBUG = ENVIRONMENT == "development"

# 开发环境特殊配置
if DEBUG:
    API_RELOAD = True
    LOG_CONFIG["level"] = "DEBUG"
    ERROR_CONFIG["include_traceback"] = True
    CORS_ORIGINS.append("*")

# ==================== 配置验证 ====================

def validate_config() -> Dict[str, Any]:
    """验证配置"""
    errors = []
    warnings = []
    
    # 检查端口范围
    if not (1 <= API_PORT <= 65535):
        errors.append(f"API端口 {API_PORT} 超出有效范围 (1-65535)")
    
    # 检查日志级别
    valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if LOG_CONFIG["level"].upper() not in valid_log_levels:
        errors.append(f"无效的日志级别: {LOG_CONFIG['level']}")
    
    # 检查数据分析天数
    if DATA_CONFIG["default_analytics_days"] not in [1, 7, 30]:
        warnings.append(f"数据分析天数 {DATA_CONFIG['default_analytics_days']} 可能不被支持")
    
    # 检查缓存配置
    if CACHE_CONFIG["ttl"] <= 0:
        warnings.append("缓存TTL设置为0或负数，缓存将不生效")
    
    return {
        "errors": errors,
        "warnings": warnings,
        "valid": len(errors) == 0
    }

# ==================== 配置导出 ====================

def get_config() -> Dict[str, Any]:
    """获取完整配置"""
    return {
        "api": {
            "host": API_HOST,
            "port": API_PORT,
            "reload": API_RELOAD,
            "log_level": API_LOG_LEVEL
        },
        "cors": {
            "origins": CORS_ORIGINS
        },
        "douyin": {
            "login": DEFAULT_LOGIN_CONFIG,
            "data": DATA_CONFIG
        },
        "cache": CACHE_CONFIG,
        "security": {
            "api_key": API_KEY,
            "enable_api_key_auth": ENABLE_API_KEY_AUTH,
            "rate_limit": RATE_LIMIT_CONFIG
        },
        "logging": LOG_CONFIG,
        "database": DATABASE_CONFIG,
        "monitoring": MONITORING_CONFIG,
        "response": RESPONSE_CONFIG,
        "error": ERROR_CONFIG,
        "features": FEATURE_FLAGS,
        "environment": ENVIRONMENT,
        "debug": DEBUG
    }

def print_config():
    """打印配置信息"""
    config = get_config()
    validation = validate_config()
    
    print("=== API配置信息 ===")
    print(f"环境: {ENVIRONMENT}")
    print(f"调试模式: {DEBUG}")
    print(f"服务地址: {API_HOST}:{API_PORT}")
    print(f"自动重载: {API_RELOAD}")
    print(f"日志级别: {API_LOG_LEVEL}")
    
    if validation["warnings"]:
        print("\n⚠️  配置警告:")
        for warning in validation["warnings"]:
            print(f"  - {warning}")
    
    if validation["errors"]:
        print("\n❌ 配置错误:")
        for error in validation["errors"]:
            print(f"  - {error}")
    else:
        print("\n✅ 配置验证通过")

if __name__ == "__main__":
    print_config()
