#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者中心API客户端示例

展示如何通过HTTP请求调用抖音创作者接口
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional

import aiohttp
import requests


class DouYinCreatorAPIClient:
    """抖音创作者API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        async with self.session.request(method, url, **kwargs) as response:
            result = await response.json()
            
            if response.status != 200:
                raise Exception(f"API请求失败: {result.get('message', '未知错误')}")
            
            return result
    
    async def get_api_info(self) -> Dict[str, Any]:
        """获取API信息"""
        return await self._request("GET", "/")
    
    async def login(self, login_type: str = "qrcode", phone: str = None, 
                   cookies: str = None, headless: bool = False) -> Dict[str, Any]:
        """登录创作者中心"""
        data = {
            "login_type": login_type,
            "headless": headless
        }
        
        if phone:
            data["phone"] = phone
        if cookies:
            data["cookies"] = cookies
        
        return await self._request("POST", "/auth/login", json=data)
    
    async def check_login_status(self) -> Dict[str, Any]:
        """检查登录状态"""
        return await self._request("GET", "/auth/status")
    
    async def get_creator_info(self) -> Dict[str, Any]:
        """获取创作者信息"""
        return await self._request("GET", "/creator/info")
    
    async def get_creator_videos(self, cursor: int = 0, count: int = 20) -> Dict[str, Any]:
        """获取创作者视频列表"""
        data = {"cursor": cursor, "count": count}
        return await self._request("POST", "/creator/videos", json=data)
    
    async def get_all_creator_videos(self) -> Dict[str, Any]:
        """获取创作者所有视频"""
        return await self._request("GET", "/creator/videos/all")
    
    async def get_video_analytics(self, item_id: str) -> Dict[str, Any]:
        """获取视频分析数据"""
        data = {"item_id": item_id}
        return await self._request("POST", "/video/analytics", json=data)
    
    async def get_creator_analytics(self, days: int = 7) -> Dict[str, Any]:
        """获取创作者数据分析"""
        data = {"days": days}
        return await self._request("POST", "/creator/analytics", json=data)
    
    async def get_fans_analytics(self, days: int = 7) -> Dict[str, Any]:
        """获取粉丝数据分析"""
        data = {"days": days}
        return await self._request("POST", "/fans/analytics", json=data)
    
    async def get_comments_management(self, cursor: int = 0, count: int = 20) -> Dict[str, Any]:
        """获取评论管理数据"""
        params = {"cursor": cursor, "count": count}
        return await self._request("GET", "/comments/management", params=params)
    
    async def reply_comment(self, comment_id: str, reply_text: str, item_id: str) -> Dict[str, Any]:
        """回复评论"""
        data = {
            "comment_id": comment_id,
            "reply_text": reply_text,
            "item_id": item_id
        }
        return await self._request("POST", "/comments/reply", json=data)
    
    async def get_income_overview(self, days: int = 7) -> Dict[str, Any]:
        """获取收益概览"""
        data = {"days": days}
        return await self._request("POST", "/income/overview", json=data)
    
    async def logout(self) -> Dict[str, Any]:
        """登出"""
        return await self._request("POST", "/auth/logout")


class SyncDouYinCreatorAPIClient:
    """同步版本的抖音创作者API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
    
    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        response = requests.request(method, url, **kwargs)
        
        if response.status_code != 200:
            raise Exception(f"API请求失败: {response.text}")
        
        return response.json()
    
    def get_api_info(self) -> Dict[str, Any]:
        """获取API信息"""
        return self._request("GET", "/")
    
    def login(self, login_type: str = "qrcode", phone: str = None, 
             cookies: str = None, headless: bool = False) -> Dict[str, Any]:
        """登录创作者中心"""
        data = {
            "login_type": login_type,
            "headless": headless
        }
        
        if phone:
            data["phone"] = phone
        if cookies:
            data["cookies"] = cookies
        
        return self._request("POST", "/auth/login", json=data)
    
    def check_login_status(self) -> Dict[str, Any]:
        """检查登录状态"""
        return self._request("GET", "/auth/status")
    
    def get_creator_info(self) -> Dict[str, Any]:
        """获取创作者信息"""
        return self._request("GET", "/creator/info")


async def async_example():
    """异步客户端使用示例"""
    print("=== 异步API客户端示例 ===")
    
    async with DouYinCreatorAPIClient() as client:
        try:
            # 1. 获取API信息
            print("1. 获取API信息...")
            api_info = await client.get_api_info()
            print(f"API信息: {api_info['data']}")
            
            # 2. 登录（二维码登录）
            print("\n2. 开始登录...")
            login_result = await client.login(login_type="qrcode", headless=False)
            print(f"登录结果: {login_result['message']}")
            
            # 等待登录完成
            print("等待登录完成...")
            for i in range(30):  # 最多等待30秒
                await asyncio.sleep(1)
                status = await client.check_login_status()
                if status['data']['logged_in']:
                    print("✅ 登录成功！")
                    break
                print(f"等待登录中... ({i+1}/30)")
            else:
                print("❌ 登录超时")
                return
            
            # 3. 获取创作者信息
            print("\n3. 获取创作者信息...")
            creator_info = await client.get_creator_info()
            print(f"创作者信息: {json.dumps(creator_info['data'], indent=2, ensure_ascii=False)}")
            
            # 4. 获取视频列表
            print("\n4. 获取视频列表...")
            videos = await client.get_creator_videos(count=5)
            print(f"视频数量: {len(videos['data'].get('item_list', []))}")
            
            # 5. 获取数据分析
            print("\n5. 获取数据分析...")
            analytics = await client.get_creator_analytics(days=7)
            print(f"7天数据分析: {json.dumps(analytics['data'], indent=2, ensure_ascii=False)}")
            
            # 6. 获取粉丝分析
            print("\n6. 获取粉丝分析...")
            fans_analytics = await client.get_fans_analytics(days=7)
            print(f"粉丝分析: {json.dumps(fans_analytics['data'], indent=2, ensure_ascii=False)}")
            
            # 7. 获取评论管理
            print("\n7. 获取评论管理...")
            comments = await client.get_comments_management(count=5)
            print(f"评论数量: {len(comments['data'].get('comments', []))}")
            
            # 8. 获取收益概览
            print("\n8. 获取收益概览...")
            income = await client.get_income_overview(days=7)
            print(f"收益概览: {json.dumps(income['data'], indent=2, ensure_ascii=False)}")
            
            print("\n🎉 所有API调用完成！")
            
        except Exception as e:
            print(f"❌ 错误: {e}")


def sync_example():
    """同步客户端使用示例"""
    print("=== 同步API客户端示例 ===")
    
    client = SyncDouYinCreatorAPIClient()
    
    try:
        # 1. 获取API信息
        print("1. 获取API信息...")
        api_info = client.get_api_info()
        print(f"API信息: {api_info['data']}")
        
        # 2. 登录
        print("\n2. 开始登录...")
        login_result = client.login(login_type="qrcode", headless=False)
        print(f"登录结果: {login_result['message']}")
        
        # 等待用户手动登录
        input("请在浏览器中完成登录，然后按回车继续...")
        
        # 3. 检查登录状态
        print("\n3. 检查登录状态...")
        status = client.check_login_status()
        if status['data']['logged_in']:
            print("✅ 已登录")
            
            # 4. 获取创作者信息
            print("\n4. 获取创作者信息...")
            creator_info = client.get_creator_info()
            print(f"创作者信息: {json.dumps(creator_info['data'], indent=2, ensure_ascii=False)}")
        else:
            print("❌ 未登录")
        
    except Exception as e:
        print(f"❌ 错误: {e}")


def curl_examples():
    """cURL命令示例"""
    print("""
=== cURL命令示例 ===

1. 获取API信息:
curl -X GET "http://localhost:8000/"

2. 登录（二维码）:
curl -X POST "http://localhost:8000/auth/login" \\
  -H "Content-Type: application/json" \\
  -d '{"login_type": "qrcode", "headless": false}'

3. 检查登录状态:
curl -X GET "http://localhost:8000/auth/status"

4. 获取创作者信息:
curl -X GET "http://localhost:8000/creator/info"

5. 获取视频列表:
curl -X POST "http://localhost:8000/creator/videos" \\
  -H "Content-Type: application/json" \\
  -d '{"cursor": 0, "count": 20}'

6. 获取数据分析:
curl -X POST "http://localhost:8000/creator/analytics" \\
  -H "Content-Type: application/json" \\
  -d '{"days": 7}'

7. 获取粉丝分析:
curl -X POST "http://localhost:8000/fans/analytics" \\
  -H "Content-Type: application/json" \\
  -d '{"days": 7}'

8. 获取评论管理:
curl -X GET "http://localhost:8000/comments/management?cursor=0&count=20"

9. 回复评论:
curl -X POST "http://localhost:8000/comments/reply" \\
  -H "Content-Type: application/json" \\
  -d '{"comment_id": "123", "reply_text": "感谢支持！", "item_id": "456"}'

10. 获取收益概览:
curl -X POST "http://localhost:8000/income/overview" \\
  -H "Content-Type: application/json" \\
  -d '{"days": 7}'

11. 登出:
curl -X POST "http://localhost:8000/auth/logout"
    """)


if __name__ == "__main__":
    print("抖音创作者中心API客户端示例")
    print("请确保API服务器已启动: python api/douyin_creator_api.py")
    print()
    
    choice = input("选择示例类型 (1: 异步, 2: 同步, 3: cURL命令): ")
    
    if choice == "1":
        asyncio.run(async_example())
    elif choice == "2":
        sync_example()
    elif choice == "3":
        curl_examples()
    else:
        print("无效选择")
