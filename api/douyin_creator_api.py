#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者中心HTTP API接口

提供RESTful API接口供外部调用抖音创作者功能
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from media_platform.douyin.core import DouYinCrawler
from media_platform.douyin.creator_client import DOUYINCreatorClient
from media_platform.douyin.login import DouYinCreatorLogin

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="抖音创作者中心API",
    description="提供抖音创作者数据获取和管理功能的HTTP API接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局变量存储爬虫实例
crawler_instance: Optional[DouYinCrawler] = None
creator_client: Optional[DOUYINCreatorClient] = None

# ==================== 请求模型 ====================

class LoginRequest(BaseModel):
    """登录请求模型"""
    login_type: str = Field(..., description="登录类型: qrcode, phone, cookie")
    phone: Optional[str] = Field(None, description="手机号（phone登录时必填）")
    cookies: Optional[str] = Field(None, description="Cookies字符串（cookie登录时必填）")
    headless: bool = Field(False, description="是否无头模式")

class VideoAnalyticsRequest(BaseModel):
    """视频分析请求模型"""
    item_id: str = Field(..., description="视频ID")

class CreatorAnalyticsRequest(BaseModel):
    """创作者分析请求模型"""
    days: int = Field(7, description="分析天数: 1, 7, 30")

class VideosRequest(BaseModel):
    """视频列表请求模型"""
    cursor: int = Field(0, description="游标位置")
    count: int = Field(20, description="获取数量")

class CommentReplyRequest(BaseModel):
    """评论回复请求模型"""
    comment_id: str = Field(..., description="评论ID")
    reply_text: str = Field(..., description="回复内容")
    item_id: str = Field(..., description="视频ID")

# ==================== 响应模型 ====================

class BaseResponse(BaseModel):
    """基础响应模型"""
    code: int = Field(0, description="状态码，0表示成功")
    message: str = Field("success", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

# ==================== 依赖函数 ====================

async def get_creator_client() -> DOUYINCreatorClient:
    """获取创作者客户端依赖"""
    global creator_client
    if not creator_client:
        raise HTTPException(status_code=401, detail="请先登录创作者中心")
    return creator_client

# ==================== API路由 ====================

@app.get("/", response_model=BaseResponse)
async def root():
    """根路径，返回API信息"""
    return BaseResponse(
        message="抖音创作者中心API服务",
        data={
            "version": "1.0.0",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    )

@app.post("/auth/login", response_model=BaseResponse)
async def login(request: LoginRequest, background_tasks: BackgroundTasks):
    """创作者登录接口"""
    global crawler_instance, creator_client
    
    try:
        # 配置登录参数
        config.PLATFORM = "douyin"
        config.CRAWLER_TYPE = "creator_center"
        config.LOGIN_TYPE = request.login_type
        config.HEADLESS = request.headless
        
        if request.login_type == "phone" and request.phone:
            config.LOGIN_PHONE = request.phone
        elif request.login_type == "cookie" and request.cookies:
            config.COOKIES = request.cookies
        
        # 创建爬虫实例
        crawler_instance = DouYinCrawler()
        
        # 后台执行登录
        background_tasks.add_task(perform_login)
        
        return BaseResponse(
            message="登录请求已提交，请等待登录完成",
            data={"login_type": request.login_type}
        )
        
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

async def perform_login():
    """执行登录操作"""
    global crawler_instance, creator_client
    try:
        if crawler_instance:
            await crawler_instance.login_creator_center()
            creator_client = crawler_instance.dy_creator_client
            logger.info("创作者中心登录成功")
    except Exception as e:
        logger.error(f"登录执行失败: {e}")

@app.get("/auth/status", response_model=BaseResponse)
async def login_status():
    """检查登录状态"""
    global creator_client, crawler_instance
    
    if not creator_client or not crawler_instance:
        return BaseResponse(
            code=401,
            message="未登录",
            data={"logged_in": False}
        )
    
    try:
        is_logged_in = await creator_client.pong(crawler_instance.browser_context)
        return BaseResponse(
            message="登录状态检查成功",
            data={"logged_in": is_logged_in}
        )
    except Exception as e:
        logger.error(f"检查登录状态失败: {e}")
        return BaseResponse(
            code=500,
            message=f"检查登录状态失败: {str(e)}",
            data={"logged_in": False}
        )

@app.get("/creator/info", response_model=BaseResponse)
async def get_creator_info(client: DOUYINCreatorClient = Depends(get_creator_client)):
    """获取创作者信息"""
    try:
        data = await client.get_creator_info()
        return BaseResponse(
            message="获取创作者信息成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取创作者信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取创作者信息失败: {str(e)}")

@app.post("/creator/videos", response_model=BaseResponse)
async def get_creator_videos(
    request: VideosRequest,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """获取创作者视频列表"""
    try:
        data = await client.get_creator_videos(
            cursor=request.cursor,
            count=request.count
        )
        return BaseResponse(
            message="获取视频列表成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取视频列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取视频列表失败: {str(e)}")

@app.get("/creator/videos/all", response_model=BaseResponse)
async def get_all_creator_videos(client: DOUYINCreatorClient = Depends(get_creator_client)):
    """获取创作者所有视频"""
    try:
        data = await client.get_all_creator_videos()
        return BaseResponse(
            message="获取所有视频成功",
            data={"videos": data, "total": len(data)}
        )
    except Exception as e:
        logger.error(f"获取所有视频失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取所有视频失败: {str(e)}")

@app.post("/video/analytics", response_model=BaseResponse)
async def get_video_analytics(
    request: VideoAnalyticsRequest,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """获取视频分析数据"""
    try:
        data = await client.get_video_analytics(request.item_id)
        return BaseResponse(
            message="获取视频分析数据成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取视频分析数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取视频分析数据失败: {str(e)}")

@app.post("/creator/analytics", response_model=BaseResponse)
async def get_creator_analytics(
    request: CreatorAnalyticsRequest,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """获取创作者数据分析"""
    try:
        data = await client.get_creator_analytics(days=request.days)
        return BaseResponse(
            message="获取创作者数据分析成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取创作者数据分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取创作者数据分析失败: {str(e)}")

@app.post("/fans/analytics", response_model=BaseResponse)
async def get_fans_analytics(
    request: CreatorAnalyticsRequest,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """获取粉丝数据分析"""
    try:
        data = await client.get_fans_analytics(days=request.days)
        return BaseResponse(
            message="获取粉丝数据分析成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取粉丝数据分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取粉丝数据分析失败: {str(e)}")

@app.get("/comments/management", response_model=BaseResponse)
async def get_comments_management(
    cursor: int = 0,
    count: int = 20,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """获取评论管理数据"""
    try:
        data = await client.get_comments_management(cursor=cursor, count=count)
        return BaseResponse(
            message="获取评论管理数据成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取评论管理数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取评论管理数据失败: {str(e)}")

@app.post("/comments/reply", response_model=BaseResponse)
async def reply_comment(
    request: CommentReplyRequest,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """回复评论"""
    try:
        data = await client.reply_comment(
            comment_id=request.comment_id,
            reply_text=request.reply_text,
            item_id=request.item_id
        )
        return BaseResponse(
            message="回复评论成功",
            data=data
        )
    except Exception as e:
        logger.error(f"回复评论失败: {e}")
        raise HTTPException(status_code=500, detail=f"回复评论失败: {str(e)}")

@app.post("/income/overview", response_model=BaseResponse)
async def get_income_overview(
    request: CreatorAnalyticsRequest,
    client: DOUYINCreatorClient = Depends(get_creator_client)
):
    """获取收益概览"""
    try:
        data = await client.get_income_overview(days=request.days)
        return BaseResponse(
            message="获取收益概览成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取收益概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取收益概览失败: {str(e)}")

@app.post("/auth/logout", response_model=BaseResponse)
async def logout():
    """登出接口"""
    global crawler_instance, creator_client
    
    try:
        if crawler_instance:
            await crawler_instance.close()
            crawler_instance = None
            creator_client = None
        
        return BaseResponse(message="登出成功")
    except Exception as e:
        logger.error(f"登出失败: {e}")
        raise HTTPException(status_code=500, detail=f"登出失败: {str(e)}")

# ==================== 异常处理 ====================

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content=BaseResponse(
            code=exc.status_code,
            message=exc.detail,
            data=None
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content=BaseResponse(
            code=500,
            message=f"服务器内部错误: {str(exc)}",
            data=None
        ).dict()
    )

# ==================== 启动函数 ====================

def start_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """启动API服务器"""
    uvicorn.run(
        "api.douyin_creator_api:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    start_server(reload=True)
