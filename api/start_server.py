#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者中心API服务器启动脚本

提供便捷的服务器启动和配置功能
"""

import argparse
import os
import sys
import logging
from typing import Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'aiohttp',
        'requests'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行以下命令安装依赖:")
        logger.info("pip install fastapi uvicorn pydantic aiohttp requests")
        logger.info("或者运行: pip install -r api/requirements.txt")

        # 提供自动安装选项
        try:
            import subprocess
            choice = input("是否自动安装缺失的依赖? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                logger.info("正在安装依赖...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install"
                ] + missing_packages)
                logger.info("依赖安装完成，请重新运行服务器")
                return True
        except Exception as e:
            logger.error(f"自动安装失败: {e}")

        return False

    return True


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    抖音创作者中心API服务                        ║
║                                                              ║
║  🚀 提供RESTful API接口访问抖音创作者功能                      ║
║  📊 支持数据分析、视频管理、评论管理、收益统计等功能              ║
║  🔐 支持多种登录方式：二维码、手机号、Cookie                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_usage_info(host: str, port: int):
    """打印使用信息"""
    print(f"""
🌐 服务器信息:
   - 服务地址: http://{host}:{port}
   - API文档: http://{host}:{port}/docs
   - ReDoc文档: http://{host}:{port}/redoc

📖 快速开始:
   1. 访问 http://{host}:{port}/docs 查看API文档
   2. 使用 POST /auth/login 接口登录
   3. 调用其他接口获取数据

🔧 客户端示例:
   python api/client_example.py

📝 cURL示例:
   curl -X GET "http://{host}:{port}/"

⚠️  注意事项:
   - 首次使用建议使用二维码登录
   - 请确保有稳定的网络连接
   - 遵守平台使用条款，合理控制请求频率
    """)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="抖音创作者中心API服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                          # 使用默认配置启动
  %(prog)s --host 0.0.0.0 --port 8080  # 指定主机和端口
  %(prog)s --reload                 # 开发模式，自动重载
  %(prog)s --log-level debug        # 设置日志级别
        """
    )

    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="服务器主机地址 (默认: 0.0.0.0)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="服务器端口 (默认: 8000)"
    )

    parser.add_argument(
        "--reload",
        action="store_true",
        help="开启自动重载模式（开发用）"
    )

    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="日志级别 (默认: info)"
    )

    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数量 (默认: 1)"
    )

    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="检查依赖并退出"
    )

    args = parser.parse_args()

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    if args.check_deps:
        logger.info("✅ 所有依赖已安装")
        sys.exit(0)

    # 设置日志级别
    log_level = getattr(logging, args.log_level.upper())
    logging.getLogger().setLevel(log_level)

    # 打印启动信息
    print_banner()
    print_usage_info(args.host, args.port)

    # 启动服务器
    logger.info(f"启动API服务器: {args.host}:{args.port}")
    logger.info(f"日志级别: {args.log_level}")
    logger.info(f"自动重载: {'开启' if args.reload else '关闭'}")

    try:
        # 在这里导入start_server，确保依赖检查已完成
        from api.douyin_creator_api import start_server

        start_server(
            host=args.host,
            port=args.port,
            reload=args.reload
        )
    except ImportError as e:
        logger.error(f"导入失败: {e}")
        logger.error("请确保已安装所有依赖")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()