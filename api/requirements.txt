# 抖音创作者中心HTTP API依赖

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# 数据验证
pydantic>=2.0.0

# HTTP客户端
aiohttp>=3.8.0
requests>=2.31.0

# 异步支持
asyncio-mqtt>=0.13.0  # 可选，用于消息队列

# 数据处理
python-multipart>=0.0.6  # 文件上传支持

# 开发工具（可选）
pytest>=7.0.0
pytest-asyncio>=0.21.0
httpx>=0.25.0  # 测试客户端

# 日志和监控（可选）
structlog>=23.0.0
prometheus-client>=0.17.0

# 安全（可选）
python-jose[cryptography]>=3.3.0  # JWT支持
passlib[bcrypt]>=1.7.4  # 密码哈希

# 数据库（可选）
sqlalchemy>=2.0.0
alembic>=1.12.0

# 缓存（可选）
redis>=5.0.0
aioredis>=2.0.0
